package iym.db.jpa.dao;


import iym.common.model.entity.iym.IymKullanicilar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface IymKullanicilarRepo extends JpaRepository<IymKullanicilar, Long> {

    // Kullanıcı adına göre arama
    Optional<IymKullanicilar> findByKullaniciAdi(String kullaniciAdi);

    // Durumu alanına göre arama
    List<IymKullanicilar> findByDurumu(String durumu);

    // Tüm kullanıcıları kullanıcı adına göre sıralı getir
    List<IymKullanicilar> findAllByOrderByKullaniciAdiAsc();

}
