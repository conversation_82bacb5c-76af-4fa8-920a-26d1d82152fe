import { Injectable, signal,computed, effect  } from '@angular/core';

import { AuthService } from '../authentication/auth.service';
import { MenuItem } from 'primeng/api';
import { MenuItemModel } from './menu-item.model';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../enviroments/environment';
import { MenuItemDto } from '../menu-yonetimi/menu-item.model';


@Injectable({
  providedIn: 'root'
})
export class MenuService {
  menuItems = signal<MenuItemModel[]>([]); 
  userRoles = signal<string[]>([]);

  constructor(private authService: AuthService,private http: HttpClient) {
    //this.userRoles.set(this.authService.getUserPermissions());
    //this.loadMenu();

    // AuthService içindeki authChanged event'ini dinle
    effect(() => {
      if (this.authService.authChanged()) {
        this.userRoles.set(this.authService.getUserPermissions());
        this.loadMenu();
      }
    });

    // İlk yükleme için men<PERSON> başlat
    this.loadMenu();
  }
loadMenuAsync(): Promise<void> {
  return new Promise((resolve) => {
    this.http.get<MenuItemDto[]>(environment.apiUrl + "/menu-items/menuagacigetir").subscribe(response => {
      const mappedMenu: MenuItemModel[] = this.mapMenuItems(response);

      // IYM menü öğelerini ekle
      const iymMenuItems = this.getIymMenuItems();
      const combinedMenu = [...mappedMenu, ...iymMenuItems];

      this.menuItems.set(combinedMenu);
      resolve();
    });
  });
}

  loadMenu() {
    // Test için: Backend çağrısını bypass et, sadece IYM menüsünü göster
    const iymMenuItems = this.getIymMenuItems();

    // Örnek dinamik menü öğeleri ekle
    const mockDynamicMenu: MenuItemModel[] = [
      {
        id: "1",
        label: "Ana Menü",
        icon: "fa-solid fa-person-digging",
        routerLink: "",
        // queryParams: null,
        parentId: null,
        menuOrder: 999,
        yetkiIds: [],
        items: [
          {
            id: "2",
            label: "Ülke Yönetimi",
            icon: "fa-solid fa-globe",
            routerLink: "ulkeler",
            // queryParams: null,
            parentId: 1,
            menuOrder: 0,
            yetkiIds: [],
            items: [],
            yetkiAdlari: []
          }
        ],
        yetkiAdlari: []
      },
      {
        id: "3",
        label: "Yetkilendirme",
        // icon: null,
        routerLink: "",
        queryParams: [],
        parentId: null,
        menuOrder: 999,
        yetkiIds: [],
        items: [
          {
            id: "4",
            label: "Yetkiler",
            icon: "fa-solid fa-drum",
            routerLink: "yetkiler",
            // queryParams: null,
            parentId: 3,
            menuOrder: 0,
            yetkiIds: [1],
            // items: null,
            yetkiAdlari: ["YETKI_VIEW"]
          },
          {
            id: "5",
            label: "Kullanıcı Gruplar",
            icon: "fa-solid fa-people-roof",
            routerLink: "kullanici-gruplar",
            // queryParams: null,
            parentId: 3,
            menuOrder: 1,
            yetkiIds: [3],
            // items: null,
            yetkiAdlari: ["KULLANICI_VIEW"]
          },
          {
            id: "6",
            label: "Kullanıcılar",
            icon: "fa-solid fa-user-tie",
            routerLink: "kullanicilar",
            // queryParams: null,
            parentId: 3,
            menuOrder: 2,
            yetkiIds: [3],
            // items: null,
            yetkiAdlari: ["KULLANICI_VIEW"]
          },
          {
            id: "7",
            label: "Menüler",
            icon: "fa-solid fa-bars",
            routerLink: "menuler",
            // queryParams: null,
            parentId: 3,
            menuOrder: 3,
            yetkiIds: [5],
            // items: null,
            yetkiAdlari: ["MENU_VIEW"]
          }
        ],
        yetkiAdlari: []
      }
    ];

    const combinedMenu = [...mockDynamicMenu, ...iymMenuItems];
    this.menuItems.set(combinedMenu);

    // Gerçek backend çağrısı (şimdilik kapalı)
    /*
    this.http.get<MenuItemDto[]>(environment.apiUrl + "/menu-items/menuagacigetir").subscribe(response => {
      const mappedMenu: MenuItemModel[] = this.mapMenuItems(response);
      const iymMenuItems = this.getIymMenuItems();
      const combinedMenu = [...mappedMenu, ...iymMenuItems];
      this.menuItems.set(combinedMenu);
    });
    */
  }
  
  // Yardımcı fonksiyon:
private mapMenuItems(menuItems: MenuItemDto[] | null | undefined): MenuItemModel[] {
  if (!menuItems) return [];

  return menuItems.map(menuItem => ({
    label: menuItem.label,
    icon: menuItem.icon,
    routerLink: menuItem.routerLink,
    roles: menuItem.yetkiAdlari || [], // ⬅️ string roller
    items: menuItem.items ? this.mapMenuItems(menuItem.items) : undefined
  }));
}


  clearMenu() {
    this.menuItems.set([]); // Çıkış yapıldığında menüyü sıfırla
  }

  hasPermission(menuRoles?: string[]): boolean {
    let hasIymRole = menuRoles?.some(role => ['IYM_USER', 'IYM_ADMIN'].includes(role));
    if (hasIymRole) {
      return true;
    }
    if (this.userRoles().includes('KullaniciTipi.Admin')) {
      return true; // Eğer kullanıcı "admin" ise her şeyi görebilir
    }

    if (!menuRoles || menuRoles.length === 0) {
      return true; // Eğer öğenin belirli bir rol gereksinimi yoksa herkese göster
    }
    let hasRole=menuRoles.some(role => this.userRoles().includes(role));
      return hasRole; // Kullanıcının herhangi bir rolü eşleşiyorsa göster
  }

private filterMenuItems(menuItems: MenuItemModel[]): MenuItemModel[] {
  return menuItems
    .map(item => ({
      ...item,
      items: item.items ? this.filterMenuItems(item.items.filter(sub => this.hasPermission(sub.roles))) : undefined
    }))
    .filter(item =>
      (item.items && item.items.length > 0) ||
      item.routerLink ||
      item.command
    );
}

getMenu = computed(() => {
  return this.filterMenuItems(this.menuItems());
});

// IYM Menü Öğelerini Döndürür
private getIymMenuItems(): MenuItemModel[] {
  return [
    {
      label: 'IYM (İş Yönetim Modülü)',
      icon: 'pi pi-shield',
      roles: ['IYM_USER', 'IYM_ADMIN'],
      items: [
        {
          label: 'Talep İşlemleri',
          icon: 'pi pi-inbox',
          roles: ['IYM_USER', 'IYM_ADMIN'],
          items: [
            {
              label: 'Talep Gönderme',
              icon: 'pi pi-send',
              routerLink: '/iym/talep-gonderme',
              roles: ['IYM_USER', 'IYM_ADMIN']
            }
          ]
        },
        {
          label: 'Evrak İşlemleri',
          icon: 'pi pi-volume-up',
          roles: ['IYM_USER', 'IYM_ADMIN'],
          items: [
            {
              label: 'Evrak Araması',
              icon: 'pi pi-search',
              routerLink: '/iym/dinleme-evrak-aramasi',
              roles: ['IYM_USER', 'IYM_ADMIN']
            }
          ]
        },
        {
          label: 'Evrak Gönderme',
          icon: 'pi pi-upload',
          routerLink: '/iym/evrak-gonderme',
          roles: ['IYM_USER', 'IYM_ADMIN']
        },
        {
          label: 'XML Sorgulama',
          icon: 'pi pi-code',
          routerLink: '/iym/xml-sorgulama',
          roles: ['IYM_USER', 'IYM_ADMIN']
        },
        {
          label: 'Parametre Sorgulama',
          icon: 'pi pi-cog',
          routerLink: '/iym/parametre-sorgulama',
          roles: ['IYM_ADMIN']
        },
        {
          label: 'İletişimin Tespiti',
          icon: 'pi pi-eye',
          routerLink: '/iym/iletisimin-tespiti',
          roles: ['IYM_USER', 'IYM_ADMIN']
        }
      ]
    }
  ];
}


}
