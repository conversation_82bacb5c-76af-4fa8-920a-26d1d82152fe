package iym.common.service.db;


import iym.common.model.entity.iym.IymKullanicilar;

import java.util.List;
import java.util.Optional;

public interface DbIymKullanicilarService extends GenericDbService<IymKullanicilar, Long> {

    /**
     * <PERSON>llan<PERSON>cı adına göre kullanıcı bulma
     * @param kullaniciAdi Kullanıcı adı
     * @return Bulunan kullanıcı
     */
    Optional<IymKullanicilar> findByKullaniciAdi(String kullaniciAdi);

    /**
     * Duruma göre kullanıcıları bulma
     * @param durumu Kullanıcı durumu
     * @return Bulunan kullanıcılar
     */
    List<IymKullanicilar> findByDurumu(String durumu);

    /**
     * Tüm kullanıcıları kullanıcı adına göre sıralı getirme
     * @return Sıralı kullanıcı listesi
     */
    List<IymKullanicilar> findAllByOrderByKullaniciAdiAsc();
}
