package iym.db.jpa.service.impl;


import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.IymKullanicilar;
import iym.common.service.db.DbIymKullanicilarService;
import iym.db.jpa.dao.IymKullanicilarRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


@Service
public class DbIymKullanicilarServiceImpl extends GenericDbServiceImpl<IymKullanicilar, Long> implements DbIymKullanicilarService {

    @Autowired
    public DbIymKullanicilarServiceImpl(IymKullanicilarRepo repository) {
        super(repository);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<IymKullanicilar> findByKullaniciAdi(String kullaniciAdi) {
        return getRepository().findByKullaniciAdi(kullaniciAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IymKullanicilar> findByDurumu(String durumu) {
        return getRepository().findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IymKullanicilar> findAllByOrderByKullaniciAdiAsc() {
        return getRepository().findAllByOrderByKullaniciAdiAsc();
    }

    public IymKullanicilarRepo getRepository() {
        return (IymKullanicilarRepo) repository;
    }

}
